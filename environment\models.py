from django.db import models
from RTRDA.model import BaseModel


class Environment(BaseModel):
    route = models.ForeignKey('Route', on_delete=models.CASCADE, related_name='environments', db_column='RouteId')
    masprovince = models.ForeignKey('MasProvince', on_delete=models.CASCADE, related_name='environments', db_column='MasProvinceId')
    masdistrict = models.ForeignKey('MasDistrict', on_delete=models.CASCADE, related_name='environments', db_column='MasDistrictId')
    masprovinceid = models.IntegerField(db_column='MasProvinceId', blank=True, null=True)
    masdistrictid = models.IntegerField(db_column='MasDistrictId', blank=True, null=True)
    parameter = models.CharField(db_column='Parameter', max_length=100, db_collation='Thai_CI_AI')
    value = models.FloatField(db_column='Value')
    unit = models.CharField(db_column='Unit', max_length=100, db_collation='Thai_CI_AI')
    measurementdate = models.DateTimeField(db_column='MeasurementDate')
    compliancestandard = models.CharField(db_column='ComplianceStandard', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    remark = models.TextField(db_column='Remark', db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Environment'


